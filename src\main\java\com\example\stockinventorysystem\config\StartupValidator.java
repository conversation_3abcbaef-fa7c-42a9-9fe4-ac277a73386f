package com.example.stockinventorysystem.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
public class StartupValidator {

    private static final Logger logger = LoggerFactory.getLogger(StartupValidator.class);

    @Value("${app.jwtSecret}")
    private String jwtSecret;

    @Value("${app.jwtExpirationMs}")
    private int jwtExpirationMs;

    @Value("${spring.datasource.url}")
    private String datasourceUrl;

    @Value("${spring.datasource.username}")
    private String datasourceUsername;

    @Value("${server.port}")
    private int serverPort;

    @EventListener(ApplicationReadyEvent.class)
    public void validateConfiguration() {
        logger.info("🔍 Validating application configuration...");

        boolean allValid = true;

        // Validate JWT configuration
        if (jwtSecret == null || jwtSecret.length() < 32) {
            logger.error("❌ JWT Secret is too short or missing. Must be at least 32 characters.");
            allValid = false;
        } else {
            logger.info("✅ JWT Secret configuration is valid");
        }

        if (jwtExpirationMs <= 0) {
            logger.error("❌ JWT Expiration time must be positive");
            allValid = false;
        } else {
            logger.info("✅ JWT Expiration configuration is valid ({} ms)", jwtExpirationMs);
        }

        // Validate database configuration
        if (datasourceUrl == null || !datasourceUrl.contains("mysql")) {
            logger.error("❌ Database URL is invalid or not MySQL");
            allValid = false;
        } else {
            logger.info("✅ Database URL configuration is valid");
        }

        if (datasourceUsername == null || datasourceUsername.trim().isEmpty()) {
            logger.error("❌ Database username is missing");
            allValid = false;
        } else {
            logger.info("✅ Database username configuration is valid");
        }

        // Validate server configuration
        if (serverPort <= 0 || serverPort > 65535) {
            logger.error("❌ Server port is invalid: {}", serverPort);
            allValid = false;
        } else {
            logger.info("✅ Server port configuration is valid: {}", serverPort);
        }

        // Check for common security issues
        validateSecurityConfiguration();

        if (allValid) {
            logger.info("🎉 All configuration validations passed!");
            logger.info("📊 Application is ready to serve requests on port {}", serverPort);
        } else {
            logger.error("💥 Configuration validation failed! Please fix the issues above.");
        }
    }

    private void validateSecurityConfiguration() {
        // Check if default credentials are being used
        if ("root".equals(datasourceUsername)) {
            logger.warn("⚠️ Using default database username 'root'. Consider using a dedicated database user for production.");
        }

        if (jwtSecret.contains("m7XJV0aSND0tFSBDwIxg2sjqOqWSRcnpvQ")) {
            logger.warn("⚠️ Using default JWT secret. Please change this for production deployment!");
        }

        if (serverPort == 8080) {
            logger.info("ℹ️ Using default port 8080. Consider using a different port for production.");
        }

        logger.info("🔒 Security configuration review completed");
    }
}
