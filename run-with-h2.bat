@echo off
echo ========================================
echo  Stock Inventory System - H2 Database
echo ========================================
echo.
echo Starting application with H2 in-memory database...
echo This is perfect for testing without MySQL setup.
echo.
echo H2 Console will be available at: http://localhost:8080/h2-console
echo Application will be available at: http://localhost:8080
echo.
echo Press Ctrl+C to stop the application
echo.

mvn clean compile
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Compilation failed! Please check the errors above.
    pause
    exit /b 1
)

echo.
echo ✅ Compilation successful! Starting application...
echo.

mvn spring-boot:run -Dspring-boot.run.profiles=test

pause
