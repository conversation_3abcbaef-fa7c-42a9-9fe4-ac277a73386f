# Product Management APIs - Postman Collection

## 📋 Overview

This Postman collection provides comprehensive testing for all Product Management APIs in the Stock Inventory System.

## 🚀 Quick Start

### 1. Import Collection
1. Open Postman
2. Click **Import**
3. Select `Product_APIs_Postman_Collection.json`
4. Import `Product_APIs_Environment.json` as environment

### 2. Set Environment
1. Select "Product APIs Environment" from the environment dropdown
2. Update variables if needed:
   - `base_url`: Your server URL (default: http://localhost:8080)
   - `admin_username`: Admin username (default: admin)
   - `admin_password`: Admin password (default: password123)

### 3. Authentication
**⚠️ IMPORTANT: Run the Login request first!**
1. Go to "🔐 Authentication (Required First)" folder
2. Run "Login to Get JWT Token"
3. JWT token will be automatically saved for other requests

## 📦 API Endpoints

### Authentication
- **POST** `/api/auth/signin` - Login to get JWT token

### Product Management
- **GET** `/api/products` - Get all products
- **GET** `/api/products/{id}` - Get product by ID
- **POST** `/api/products` - Create new product
- **PUT** `/api/products/{id}` - Update product
- **DELETE** `/api/products/{id}` - Delete product

## 🔒 Security & Roles

| Endpoint | Required Role |
|----------|---------------|
| GET /api/products | USER, MODERATOR, ADMIN |
| GET /api/products/{id} | USER, MODERATOR, ADMIN |
| POST /api/products | MODERATOR, ADMIN |
| PUT /api/products/{id} | MODERATOR, ADMIN |
| DELETE /api/products/{id} | ADMIN only |

## 📝 Request Examples

### Create Product Request Body
```json
{
    "code": "PROD001",
    "name": "Gaming Laptop",
    "brandName": "ASUS",
    "category": "Electronics",
    "color": "Black",
    "price": 1299.99,
    "description": "High-performance gaming laptop with RTX graphics",
    "thumbnailImage": "laptop_main.jpg",
    "subImages": "laptop_side.jpg,laptop_back.jpg",
    "availability": true
}
```

### Update Product Request Body
```json
{
    "code": "PROD001",
    "name": "Updated Gaming Laptop",
    "brandName": "ASUS ROG",
    "category": "Electronics",
    "color": "Red",
    "price": 1499.99,
    "description": "Updated high-performance gaming laptop with RTX 4080",
    "thumbnailImage": "laptop_updated.jpg",
    "subImages": "laptop_updated_side.jpg",
    "availability": true
}
```

## 📊 Response Examples

### Product Response
```json
{
    "id": 1,
    "code": "PROD001",
    "product": "Gaming Laptop",
    "brand": "ASUS",
    "category": "Electronics",
    "color": "Black",
    "price": 1299.99,
    "description": "High-performance gaming laptop",
    "createdBy": "admin",
    "createdOn": "2024-01-15T10:30:00",
    "available": true
}
```

## ✅ Testing Features

### Automated Tests
Each request includes automated tests that verify:
- ✅ Status codes
- ✅ Response structure
- ✅ Data validation
- ✅ Error handling

### Variables
- `{{base_url}}` - Server URL
- `{{jwt_token}}` - Authentication token (auto-saved)
- `{{product_id}}` - Product ID (auto-saved from responses)

## 🔧 Environment Variables

| Variable | Description | Default Value |
|----------|-------------|---------------|
| base_url | Server URL | http://localhost:8080 |
| jwt_token | JWT authentication token | (auto-saved) |
| admin_username | Admin username | admin |
| admin_password | Admin password | password123 |
| product_id | Test product ID | 1 |
| test_product_code | Test product code | PROD001 |

## 🚨 Common Issues

### 401 Unauthorized
- **Solution**: Run the Login request first to get JWT token

### 403 Forbidden
- **Solution**: Ensure you have the required role (MODERATOR/ADMIN for create/update/delete)

### 400 Bad Request
- **Solution**: Check request body format and required fields

### 404 Not Found
- **Solution**: Verify the product ID exists

## 📱 Usage Tips

1. **Always login first** - JWT token expires after 24 hours
2. **Use variables** - Leverage `{{product_id}}` for dynamic testing
3. **Check tests** - Review the test results tab for validation
4. **Environment switching** - Easy to switch between dev/staging/prod

## 🎯 Test Scenarios

### Happy Path
1. Login → Get All Products → Create Product → Get Product by ID → Update Product → Delete Product

### Error Scenarios
1. Create product with duplicate code
2. Update non-existent product
3. Delete non-existent product
4. Access without authentication

## 📞 Support

For issues or questions:
1. Check the application logs
2. Verify database connectivity
3. Ensure proper authentication
4. Review API documentation
