# Test Database Configuration (H2 In-Memory)
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# H2 Console (for debugging)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA Configuration for H2
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.properties.hibernate.format_sql=true

# JWT Configuration
app.jwtSecret=testSecretKeyForH2DatabaseTesting123456789012345678901234567890
app.jwtExpirationMs=86400000

# Server Configuration
server.port=8080
server.error.include-message=always

# Logging Configuration
logging.level.com.example.stockinventorysystem=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# Disable security for testing
spring.security.require-ssl=false
