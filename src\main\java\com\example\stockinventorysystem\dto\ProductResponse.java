package com.example.stockinventorysystem.dto;

import java.time.LocalDateTime;

public class ProductResponse {
    private Long id;
    private String code;
    private String name;
    private String brand;
    private String category;
    private Double price;
    private String color;
    private String description;
    private String thumbnailImage;
    private String subImages;
    private boolean available;
    private String createdBy;
    private LocalDateTime createdOn;

    // Default constructor
    public ProductResponse() {}

    // Constructor with all fields
    public ProductResponse(Long id, String code, String name, String brand, String category,
                         String color, Double price, String description, String thumbnailImage,
                         String subImages, String createdBy, LocalDateTime createdOn,
                         boolean available) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.brand = brand;
        this.category = category;
        this.color = color;
        this.price = price;
        this.description = description;
        this.thumbnailImage = thumbnailImage;
        this.subImages = subImages;
        this.createdBy = createdBy;
        this.createdOn = createdOn;
        this.available = available;
    }

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getThumbnailImage() {
        return thumbnailImage;
    }

    public void setThumbnailImage(String thumbnailImage) {
        this.thumbnailImage = thumbnailImage;
    }

    public String getSubImages() {
        return subImages;
    }

    public void setSubImages(String subImages) {
        this.subImages = subImages;
    }

    public boolean isAvailable() {
        return available;
    }

    public void setAvailable(boolean available) {
        this.available = available;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedOn() {
        return createdOn;
    }

    public void setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
    }
}