

package com.example.stockinventorysystem.controller;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/health")
public class HealthController {

    private static final Logger logger = LoggerFactory.getLogger(HealthController.class);

    @Autowired
    private DataSource dataSource;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @GetMapping
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("timestamp", LocalDateTime.now());
        health.put("status", "UP");
        health.put("application", "Stock Inventory System");
        health.put("version", "1.0.0");

        // Check database connectivity
        Map<String, Object> database = checkDatabaseHealth();
        health.put("database", database);

        // Check memory usage
        Map<String, Object> memory = checkMemoryHealth();
        health.put("memory", memory);

        // Determine overall status
        boolean isHealthy = "UP".equals(database.get("status"));
        
        if (isHealthy) {
            health.put("status", "UP");
            return ResponseEntity.ok(health);
        } else {
            health.put("status", "DOWN");
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(health);
        }
    }

    @GetMapping("/database")
    public ResponseEntity<Map<String, Object>> databaseHealth() {
        Map<String, Object> database = checkDatabaseHealth();
        
        if ("UP".equals(database.get("status"))) {
            return ResponseEntity.ok(database);
        } else {
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(database);
        }
    }

    private Map<String, Object> checkDatabaseHealth() {
        Map<String, Object> database = new HashMap<>();
        
        try {
            // Test basic connectivity
            jdbcTemplate.execute("SELECT 1");
            database.put("status", "UP");
            
            // Get database info
            String version = jdbcTemplate.queryForObject("SELECT VERSION()", String.class);
            database.put("version", version);
            
            // Check connection pool
            database.put("dataSource", dataSource.getClass().getSimpleName());
            
            logger.debug("Database health check passed");
            
        } catch (DataAccessException e) {
            database.put("status", "DOWN");
            database.put("error", e.getMessage());
            logger.error("Database health check failed: {}", e.getMessage());
        }
        
        return database;
    }

    private Map<String, Object> checkMemoryHealth() {
        Map<String, Object> memory = new HashMap<>();
        
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        memory.put("max", formatBytes(maxMemory));
        memory.put("total", formatBytes(totalMemory));
        memory.put("used", formatBytes(usedMemory));
        memory.put("free", formatBytes(freeMemory));
        
        double usagePercentage = (double) usedMemory / totalMemory * 100;
        memory.put("usagePercentage", String.format("%.2f%%", usagePercentage));
        
        if (usagePercentage > 90) {
            memory.put("status", "WARNING");
        } else {
            memory.put("status", "OK");
        }
        
        return memory;
    }

    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }
}
