# Stock Inventory Management System

[![License: MIT](https://img.shields.io/badge/License-MIT-blue.svg)](LICENSE)

A comprehensive Spring Boot application for managing stock inventory, purchases, invoices, and employee attendance. This enterprise-grade application provides a complete solution for businesses to manage their inventory, track purchases, generate invoices, and monitor employee attendance.

## 🌟 Key Features

- **Complete Inventory Management**: Track products, stock levels, and inventory movements
- **Purchase Management**: Manage vendors, purchase orders, and receiving
- **Invoice System**: Generate, track, and manage invoices with approval workflow
- **Employee Attendance**: Track employee time and attendance
- **Secure Authentication**: JWT-based security with role-based access control
- **REST APIs**: Well-documented API endpoints for all functionality
- **Database Flexibility**: Supports both H2 (development) and MySQL (production)

## 🚀 Quick Start (Easiest Way)

### Option 1: Run with H2 Database (No Setup Required)

**Windows:**
```bash
# Double-click this file or run in command prompt:
run-with-h2.bat
```

**Manual Command:**
```bash
cd stockInventory-main/StockInventory
mvn spring-boot:run -Dspring-boot.run.profiles=test
```

**Access:**
- **Application**: http://localhost:8080
- **H2 Console**: http://localhost:8080/h2-console
  - JDBC URL: `jdbc:h2:mem:testdb`
  - Username: `sa`
  - Password: (leave empty)

### Option 2: Run with MySQL Database

**Prerequisites:**
1. Install MySQL Server
2. Start MySQL service
3. Create database (optional - will be created automatically)

**Windows:**
```bash
# Double-click this file or run in command prompt:
run-with-mysql.bat
```

**Manual Command:**
```bash
cd stockInventory-main/StockInventory
mvn spring-boot:run
```

**Database Setup (if needed):**
```sql
-- Run this in MySQL:
CREATE DATABASE IF NOT EXISTS stockinventory;
```

## 🔧 Configuration

### Database Settings (application.properties)
- **MySQL Port**: 3306 (fixed from incorrect 8080)
- **Database**: stockinventory
- **Username**: root
- **Password**: 6666

### Test Settings (application-test.properties)
- **Database**: H2 in-memory
- **Auto-creates tables**: Yes
- **H2 Console**: Enabled

## 📋 Features

- **User Management**: Registration, authentication, role-based access
- **Product Management**: CRUD operations for products
- **Purchase Management**: Track purchases and vendors
- **Invoice Management**: Generate and manage invoices
- **Employee Attendance**: Track employee attendance and hours
- **Security**: JWT-based authentication
- **REST API**: Complete API endpoints

## 🛠️ Development

### Build and Test
```bash
# Compile
mvn clean compile

# Run tests (with MySQL)
mvn test

# Package
mvn clean package
```

### API Endpoints
- **Auth**: `/api/auth/*`
- **Products**: `/api/products/*`
- **Purchases**: `/api/purchases/*`
- **Invoices**: `/api/invoices/*`
- **Attendance**: `/api/attendance/*`

## 🔒 Security

- JWT-based authentication
- Role-based authorization (USER, MODERATOR, ADMIN)
- Password encryption
- CORS configuration
- Security headers

## 📝 Notes

- **H2 Database**: Perfect for development and testing
- **MySQL Database**: Use for production
- **Auto-configuration**: Tables created automatically
- **Default Roles**: Created on startup
- **Logging**: Configured for debugging

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/auth/signup` - Register a new user
- `POST /api/auth/signin` - Login and get JWT token

### Invoice Endpoints
- `GET /api/invoices` - List all invoices
- `GET /api/invoices/{id}` - Get invoice by ID
- `GET /api/invoices/invoice/{invoiceId}` - Get invoice by invoice number
- `GET /api/invoices/client/{clientName}` - Get invoices by client
- `GET /api/invoices/status/{status}` - Get invoices by status
- `GET /api/invoices/date-range` - Get invoices within date range
- `GET /api/invoices/pending-approval` - Get pending approval invoices
- `GET /api/invoices/total-amount/{status}` - Get total amount by status
- `POST /api/invoices` - Create new invoice
- `PUT /api/invoices/{id}` - Update invoice
- `PATCH /api/invoices/{id}/status` - Update payment status
- `PATCH /api/invoices/{id}/approve` - Approve invoice
- `DELETE /api/invoices/{id}` - Delete invoice

### Stock Management Endpoints
- `GET /api/products` - List all products
- `GET /api/purchases` - List all purchases
- `POST /api/products` - Add new product
- `POST /api/purchases` - Record new purchase

### Employee Management
- `GET /api/attendance` - Get attendance records
- `POST /api/attendance/clock-in` - Clock in
- `POST /api/attendance/clock-out` - Clock out

## 📝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Spring Boot and Spring Security teams
- JWT Authentication community
- All contributors who have helped improve this project
