package com.example.stockinventorysystem.dto;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

import com.example.stockinventorysystem.model.EAttendanceStatus;

public class AttendanceListResponse {
    private String empId;
    private String firstName;
    private String lastName;
    private List<String> roles;
    private LocalDate attendanceDate;
    private LocalTime inTime;
    private LocalTime outTime;
    private EAttendanceStatus status;
    private Double totalHours;
    private String remarks;

    // Constructors
    public AttendanceListResponse() {
    }

    public AttendanceListResponse(String empId, String firstName, String lastName, 
                                 List<String> roles, LocalDate attendanceDate, 
                                 LocalTime inTime, LocalTime outTime, 
                                 EAttendanceStatus status, Double totalHours, String remarks) {
        this.empId = empId;
        this.firstName = firstName;
        this.lastName = lastName;
        this.roles = roles;
        this.attendanceDate = attendanceDate;
        this.inTime = inTime;
        this.outTime = outTime;
        this.status = status;
        this.totalHours = totalHours;
        this.remarks = remarks;
    }

    // Getters and Setters
    public String getEmpId() {
        return empId;
    }

    public void setEmpId(String empId) {
        this.empId = empId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public List<String> getRoles() {
        return roles;
    }

    public void setRoles(List<String> roles) {
        this.roles = roles;
    }

    public LocalDate getAttendanceDate() {
        return attendanceDate;
    }

    public void setAttendanceDate(LocalDate attendanceDate) {
        this.attendanceDate = attendanceDate;
    }

    public LocalTime getInTime() {
        return inTime;
    }

    public void setInTime(LocalTime inTime) {
        this.inTime = inTime;
    }

    public LocalTime getOutTime() {
        return outTime;
    }

    public void setOutTime(LocalTime outTime) {
        this.outTime = outTime;
    }

    public EAttendanceStatus getStatus() {
        return status;
    }

    public void setStatus(EAttendanceStatus status) {
        this.status = status;
    }

    public Double getTotalHours() {
        return totalHours;
    }

    public void setTotalHours(Double totalHours) {
        this.totalHours = totalHours;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
}
