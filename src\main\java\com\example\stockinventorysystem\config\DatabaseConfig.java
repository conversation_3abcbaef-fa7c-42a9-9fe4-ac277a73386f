package com.example.stockinventorysystem.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Component
public class DatabaseConfig {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseConfig.class);

    private final JdbcTemplate jdbcTemplate;

    public DatabaseConfig(JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    @EventListener(ApplicationReadyEvent.class)
    public void validateDatabaseConnection() {
        try {
            // Test database connection
            jdbcTemplate.execute("SELECT 1");
            logger.info("✅ Database connection successful");
            
            // Check if required tables exist and create them if needed
            validateAndCreateTables();
            
        } catch (DataAccessException e) {
            logger.error("❌ Database connection failed: {}", e.getMessage());
            logger.error("Please ensure MySQL is running and the database 'stockinventory' exists");
            logger.error("You can create the database with: CREATE DATABASE stockinventory;");
        }
    }

    private void validateAndCreateTables() {
        try {
            // Check if roles table exists and has default roles
            ensureDefaultRoles();
            logger.info("✅ Database tables validated successfully");
        } catch (Exception e) {
            logger.warn("⚠️ Could not validate/create default data: {}", e.getMessage());
        }
    }

    private void ensureDefaultRoles() {
        try {
            // Check if roles exist
            Integer roleCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM roles", Integer.class);

            if (roleCount == null || roleCount == 0) {
                logger.info("Creating default roles...");
                jdbcTemplate.execute(
                    "INSERT INTO roles (name) VALUES ('ROLE_USER'), ('ROLE_MODERATOR'), ('ROLE_ADMIN') " +
                    "ON DUPLICATE KEY UPDATE name = VALUES(name)"
                );
                logger.info("✅ Default roles created");
            }
        } catch (DataAccessException e) {
            logger.debug("Roles table may not exist yet, will be created by Hibernate: {}", e.getMessage());
        } catch (Exception e) {
            logger.warn("Unexpected error while ensuring default roles: {}", e.getMessage());
        }
    }
}
