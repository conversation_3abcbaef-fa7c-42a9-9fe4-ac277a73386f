package com.example.stockinventorysystem.repository;

import com.example.stockinventorysystem.model.Attendance;
import com.example.stockinventorysystem.model.EAttendanceStatus;
import com.example.stockinventorysystem.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface AttendanceRepository extends JpaRepository<Attendance, Long> {
    
    // Find attendance by employee and date
    Optional<Attendance> findByEmployeeAndAttendanceDate(User employee, LocalDate attendanceDate);
    
    // Find all attendance records for an employee
    List<Attendance> findByEmployeeOrderByAttendanceDateDesc(User employee);
    
    // Find attendance by employee ID and date range
    List<Attendance> findByEmployeeIdAndAttendanceDateBetween(Long employeeId, LocalDate startDate, LocalDate endDate);
    
    // Find attendance by date range
    List<Attendance> findByAttendanceDateBetween(LocalDate startDate, LocalDate endDate);
    
    // Find attendance by status
    List<Attendance> findByStatus(EAttendanceStatus status);
    
    // Find attendance by date and status
    List<Attendance> findByAttendanceDateAndStatus(LocalDate date, EAttendanceStatus status);
    
    // Find today's attendance
    @Query("SELECT a FROM Attendance a WHERE a.attendanceDate = :date ORDER BY a.employee.firstName, a.employee.lastName")
    List<Attendance> findTodaysAttendance(@Param("date") LocalDate date);
    
    // Find attendance for a specific month
    @Query("SELECT a FROM Attendance a WHERE YEAR(a.attendanceDate) = :year AND MONTH(a.attendanceDate) = :month ORDER BY a.attendanceDate DESC")
    List<Attendance> findByYearAndMonth(@Param("year") int year, @Param("month") int month);
    
    // Find employees who are present today
    @Query("SELECT a FROM Attendance a WHERE a.attendanceDate = :date AND a.status = 'PRESENT' ORDER BY a.employee.firstName")
    List<Attendance> findPresentEmployees(@Param("date") LocalDate date);
    
    // Find employees who are absent today
    @Query("SELECT a FROM Attendance a WHERE a.attendanceDate = :date AND a.status = 'ABSENT' ORDER BY a.employee.firstName")
    List<Attendance> findAbsentEmployees(@Param("date") LocalDate date);
    
    // Check if attendance exists for employee on date
    boolean existsByEmployeeAndAttendanceDate(User employee, LocalDate attendanceDate);
    
    // Get attendance summary for date range
    @Query("SELECT a.status, COUNT(a) FROM Attendance a WHERE a.attendanceDate BETWEEN :startDate AND :endDate GROUP BY a.status")
    List<Object[]> getAttendanceSummary(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    // Get employee attendance count by status
    @Query("SELECT COUNT(a) FROM Attendance a WHERE a.employee.id = :employeeId AND a.status = :status AND a.attendanceDate BETWEEN :startDate AND :endDate")
    Long countByEmployeeAndStatusAndDateRange(@Param("employeeId") Long employeeId, @Param("status") EAttendanceStatus status, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
}
