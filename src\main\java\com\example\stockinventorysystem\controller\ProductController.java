package com.example.stockinventorysystem.controller;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.example.stockinventorysystem.dto.MessageResponse;
import com.example.stockinventorysystem.dto.ProductRequest;
import com.example.stockinventorysystem.dto.ProductResponse;
import com.example.stockinventorysystem.model.Product;
import com.example.stockinventorysystem.model.User;
import com.example.stockinventorysystem.repository.ProductRepository;
import com.example.stockinventorysystem.repository.UserRepository;

import jakarta.validation.Valid;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/products")
public class ProductController {
    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private UserRepository userRepository;

    @GetMapping
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<ProductResponse>> getAllProducts() {
        List<Product> products = productRepository.findAll();
        List<ProductResponse> productResponses = products.stream()
                .map(this::convertToProductResponse)
                .collect(Collectors.toList());
        return ResponseEntity.ok(productResponses);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<?> getProductById(@PathVariable Long id) {
        Optional<Product> product = productRepository.findById(id);
        return product.map(p -> ResponseEntity.ok(convertToProductResponse(p)))
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<?> createProduct(@Valid @RequestBody ProductRequest productRequest) {
        // Validate product code
        if (productRepository.existsByCode(productRequest.getCode())) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Product code already exists!"));
        }

        // Create new product with required fields
        Product product = new Product();
        
        // Required fields in the specified order
        product.setCode(productRequest.getCode());           // Product Code
        product.setBrand(productRequest.getBrand());         // Product Brand
        product.setPrice(productRequest.getPrice());         // Product Price
        product.setDescription(productRequest.getDescription()); // Product Description
        product.setName(productRequest.getName());           // Product Name
        product.setCategory(productRequest.getCategory());    // Product Category
        product.setColor(productRequest.getColor());         // Product Color

        // Set the creator of the product
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = authentication.getName();
        User currentUser = userRepository.findByUsername(username)
                .orElseThrow(() -> new RuntimeException("Error: User not found."));
        product.setCreatedBy(currentUser);

        Product savedProduct = productRepository.save(product);
        return ResponseEntity.status(HttpStatus.CREATED).body(convertToProductResponse(savedProduct));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<?> updateProduct(@PathVariable Long id, @Valid @RequestBody ProductRequest productRequest) {
        Optional<Product> existingProduct = productRepository.findById(id);
        if (!existingProduct.isPresent()) {
            return ResponseEntity.notFound().build();
        }

        // Check if the new code already exists for a different product
        if (!existingProduct.get().getCode().equals(productRequest.getCode()) &&
            productRepository.existsByCode(productRequest.getCode())) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Product code already exists!"));
        }        Product product = existingProduct.get();
        product.setCode(productRequest.getCode());
        product.setName(productRequest.getName());
        product.setBrand(productRequest.getBrand());
        product.setCategory(productRequest.getCategory());
        product.setPrice(productRequest.getPrice());
        product.setColor(productRequest.getColor());
        product.setDescription(productRequest.getDescription());

        Product updatedProduct = productRepository.save(product);
        return ResponseEntity.ok(convertToProductResponse(updatedProduct));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteProduct(@PathVariable Long id) {
        return productRepository.findById(id)
                .map(product -> {
                    productRepository.delete(product);
                    return ResponseEntity.ok(new MessageResponse("Product deleted successfully"));
                })
                .orElse(ResponseEntity.notFound().build());
    }    private ProductResponse convertToProductResponse(Product product) {
        return new ProductResponse(
            product.getId(),
            product.getCode(),
            product.getName(),
            product.getBrand(),
            product.getCategory(),
            product.getColor(),
            product.getPrice(),
            product.getDescription(),
            product.getCreatedBy() != null ? product.getCreatedBy().getUsername() : null,
            product.getCreatedOn(),
            product.isAvailable()
        );
    }
}
