package com.example.stockinventorysystem.controller;

import com.example.stockinventorysystem.dto.EmployeeRequest;
import com.example.stockinventorysystem.dto.EmployeeResponse;
import com.example.stockinventorysystem.dto.MessageResponse;
import com.example.stockinventorysystem.model.ERole;
import com.example.stockinventorysystem.model.Role;
import com.example.stockinventorysystem.model.User;
import com.example.stockinventorysystem.repository.RoleRepository;
import com.example.stockinventorysystem.repository.UserRepository;

import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/employees")
public class EmployeeController {

    @Autowired
    UserRepository userRepository;

    @Autowired
    RoleRepository roleRepository;

    @Autowired
    PasswordEncoder encoder;

    @GetMapping
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<EmployeeResponse>> getAllEmployees() {
        List<User> employees = userRepository.findAllEmployees();
        List<EmployeeResponse> employeeResponses = employees.stream()
                .map(this::convertToEmployeeResponse)
                .collect(Collectors.toList());

        return ResponseEntity.ok(employeeResponses);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<EmployeeResponse> getEmployeeById(@PathVariable Long id) {
        Optional<User> employeeOpt = userRepository.findById(id);
        if (employeeOpt.isPresent() && employeeOpt.get().getEmployeeId() != null) {
            return ResponseEntity.ok(convertToEmployeeResponse(employeeOpt.get()));
        }
        return ResponseEntity.notFound().build();
    }

    @GetMapping("/empid/{employeeId}")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<EmployeeResponse> getEmployeeByEmployeeId(@PathVariable String employeeId) {
        Optional<User> employeeOpt = userRepository.findByEmployeeId(employeeId);
        if (employeeOpt.isPresent()) {
            return ResponseEntity.ok(convertToEmployeeResponse(employeeOpt.get()));
        }
        return ResponseEntity.notFound().build();
    }

    @GetMapping("/search")
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<EmployeeResponse>> searchEmployeesByName(@RequestParam String name) {
        List<User> employees = userRepository.findEmployeesByName(name);
        List<EmployeeResponse> employeeResponses = employees.stream()
                .map(this::convertToEmployeeResponse)
                .collect(Collectors.toList());

        return ResponseEntity.ok(employeeResponses);
    }

    @PostMapping
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<?> createEmployee(@Valid @RequestBody EmployeeRequest employeeRequest) {
        if (userRepository.existsByUsername(employeeRequest.getUsername())) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Username is already taken!"));
        }

        if (userRepository.existsByEmail(employeeRequest.getEmail())) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Email is already in use!"));
        }

        if (userRepository.existsByEmployeeId(employeeRequest.getEmployeeId())) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Employee ID is already in use!"));
        }

        // Create new employee
        User employee = new User(employeeRequest.getUsername(),
                employeeRequest.getEmail(),
                encoder.encode(employeeRequest.getPassword()));
        
        employee.setFirstName(employeeRequest.getFirstName());
        employee.setLastName(employeeRequest.getLastName());
        employee.setEmployeeId(employeeRequest.getEmployeeId());

        Set<String> strRoles = employeeRequest.getRole();
        Set<Role> roles = new HashSet<>();

        if (strRoles == null) {
            Role userRole = roleRepository.findByName(ERole.ROLE_USER)
                    .orElseThrow(() -> new RuntimeException("Error: Role is not found."));
            roles.add(userRole);
        } else {
            strRoles.forEach(role -> {
                switch (role) {
                    case "admin":
                        Role adminRole = roleRepository.findByName(ERole.ROLE_ADMIN)
                                .orElseThrow(() -> new RuntimeException("Error: Role is not found."));
                        roles.add(adminRole);
                        break;
                    case "mod":
                        Role modRole = roleRepository.findByName(ERole.ROLE_MODERATOR)
                                .orElseThrow(() -> new RuntimeException("Error: Role is not found."));
                        roles.add(modRole);
                        break;
                    default:
                        Role userRole = roleRepository.findByName(ERole.ROLE_USER)
                                .orElseThrow(() -> new RuntimeException("Error: Role is not found."));
                        roles.add(userRole);
                }
            });
        }

        employee.setRoles(roles);
        User savedEmployee = userRepository.save(employee);

        return ResponseEntity.ok(new MessageResponse("Employee created successfully!"));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<?> updateEmployee(@PathVariable Long id, @Valid @RequestBody EmployeeRequest employeeRequest) {
        Optional<User> employeeOpt = userRepository.findById(id);
        if (!employeeOpt.isPresent() || employeeOpt.get().getEmployeeId() == null) {
            return ResponseEntity.notFound().build();
        }

        User employee = employeeOpt.get();

        // Check if username is being changed and if it's already taken
        if (!employee.getUsername().equals(employeeRequest.getUsername()) &&
                userRepository.existsByUsername(employeeRequest.getUsername())) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Username is already taken!"));
        }

        // Check if email is being changed and if it's already taken
        if (!employee.getEmail().equals(employeeRequest.getEmail()) &&
                userRepository.existsByEmail(employeeRequest.getEmail())) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Email is already in use!"));
        }

        // Check if employee ID is being changed and if it's already taken
        if (!employee.getEmployeeId().equals(employeeRequest.getEmployeeId()) &&
                userRepository.existsByEmployeeId(employeeRequest.getEmployeeId())) {
            return ResponseEntity.badRequest()
                    .body(new MessageResponse("Error: Employee ID is already in use!"));
        }

        // Update employee details
        employee.setUsername(employeeRequest.getUsername());
        employee.setEmail(employeeRequest.getEmail());
        employee.setFirstName(employeeRequest.getFirstName());
        employee.setLastName(employeeRequest.getLastName());
        employee.setEmployeeId(employeeRequest.getEmployeeId());

        // Update password if provided
        if (employeeRequest.getPassword() != null && !employeeRequest.getPassword().isEmpty()) {
            employee.setPassword(encoder.encode(employeeRequest.getPassword()));
        }

        // Update roles if provided
        if (employeeRequest.getRole() != null) {
            Set<String> strRoles = employeeRequest.getRole();
            Set<Role> roles = new HashSet<>();

            strRoles.forEach(role -> {
                switch (role) {
                    case "admin":
                        Role adminRole = roleRepository.findByName(ERole.ROLE_ADMIN)
                                .orElseThrow(() -> new RuntimeException("Error: Role is not found."));
                        roles.add(adminRole);
                        break;
                    case "mod":
                        Role modRole = roleRepository.findByName(ERole.ROLE_MODERATOR)
                                .orElseThrow(() -> new RuntimeException("Error: Role is not found."));
                        roles.add(modRole);
                        break;
                    default:
                        Role userRole = roleRepository.findByName(ERole.ROLE_USER)
                                .orElseThrow(() -> new RuntimeException("Error: Role is not found."));
                        roles.add(userRole);
                }
            });

            employee.setRoles(roles);
        }

        userRepository.save(employee);

        return ResponseEntity.ok(new MessageResponse("Employee updated successfully!"));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteEmployee(@PathVariable Long id) {
        Optional<User> employeeOpt = userRepository.findById(id);
        if (!employeeOpt.isPresent() || employeeOpt.get().getEmployeeId() == null) {
            return ResponseEntity.notFound().build();
        }

        userRepository.deleteById(id);
        return ResponseEntity.ok(new MessageResponse("Employee deleted successfully!"));
    }

    private EmployeeResponse convertToEmployeeResponse(User employee) {
        List<String> roles = employee.getRoles().stream()
                .map(role -> role.getName().name())
                .collect(Collectors.toList());

        return new EmployeeResponse(
                employee.getId(),
                employee.getUsername(),
                employee.getEmail(),
                employee.getFirstName(),
                employee.getLastName(),
                employee.getEmployeeId(),
                roles
        );
    }
}
