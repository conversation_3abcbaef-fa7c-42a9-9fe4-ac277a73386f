-- MySQL Database Setup Script
-- Run this script to create the database and user

-- <PERSON>reate database
CREATE DATABASE IF NOT EXISTS stockinventory;

-- Use the database
USE stockinventory;

-- Create user (optional - you can use root)
-- CREATE USER IF NOT EXISTS 'stockuser'@'localhost' IDENTIFIED BY 'stockpass';
-- GRANT ALL PRIVILEGES ON stockinventory.* TO 'stockuser'@'localhost';
-- FLUSH PRIVILEGES;

-- Show tables (will be empty initially - Hibernate will create them)
SHOW TABLES;
