{"id": "product-apis-environment", "name": "Product APIs Environment", "values": [{"key": "base_url", "value": "http://localhost:8080", "type": "default", "enabled": true}, {"key": "jwt_token", "value": "", "type": "secret", "enabled": true}, {"key": "admin_username", "value": "admin", "type": "default", "enabled": true}, {"key": "admin_password", "value": "password123", "type": "secret", "enabled": true}, {"key": "product_id", "value": "1", "type": "default", "enabled": true}, {"key": "test_product_code", "value": "PROD001", "type": "default", "enabled": true}], "_postman_variable_scope": "environment"}