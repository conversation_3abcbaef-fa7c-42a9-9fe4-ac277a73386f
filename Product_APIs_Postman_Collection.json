{"info": {"_postman_id": "product-apis-collection", "name": "Stock Inventory - Product APIs", "description": "Complete Product Management API collection for Stock Inventory System", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "product-management"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set base URL if not set", "if (!pm.collectionVariables.get('base_url')) {", "    pm.collectionVariables.set('base_url', 'http://localhost:8080');", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "jwt_token", "value": "", "type": "string"}, {"key": "product_id", "value": "1", "type": "string"}], "item": [{"name": "🔐 Authentication (Required First)", "item": [{"name": "Login to Get JWT Token", "event": [{"listen": "test", "script": {"exec": ["// Save JWT token automatically", "if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('jwt_token', response.accessToken);", "    pm.environment.set('jwt_token', response.accessToken);", "    console.log('✅ JWT <PERSON> saved successfully');", "    console.log('Token:', response.accessToken);", "} else {", "    console.log('❌ <PERSON><PERSON> failed');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"admin\",\n    \"password\": \"password123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/auth/signin", "host": ["{{base_url}}"], "path": ["api", "auth", "signin"]}, "description": "Login with admin credentials to get JWT token. Token will be automatically saved for other requests."}, "response": [{"name": "Successful Login", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"username\": \"admin\",\n    \"password\": \"password123\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/auth/signin", "host": ["{{base_url}}"], "path": ["api", "auth", "signin"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"accessToken\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n    \"tokenType\": \"Bearer\",\n    \"id\": 1,\n    \"username\": \"admin\",\n    \"email\": \"<EMAIL>\",\n    \"roles\": [\"ROLE_ADMIN\"]\n}"}]}]}, {"name": "📦 Product Management", "item": [{"name": "Get All Products", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is an array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});", "", "// Save first product ID for other tests", "if (pm.response.code === 200) {", "    const products = pm.response.json();", "    if (products.length > 0) {", "        pm.collectionVariables.set('product_id', products[0].id);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/products", "host": ["{{base_url}}"], "path": ["api", "products"]}, "description": "Retrieve all products from the inventory. Requires USER, MODERATOR, or ADMIN role."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/products", "host": ["{{base_url}}"], "path": ["api", "products"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "[\n    {\n        \"id\": 1,\n        \"code\": \"PROD001\",\n        \"product\": \"Laptop\",\n        \"brand\": \"Dell\",\n        \"category\": \"Electronics\",\n        \"color\": \"Black\",\n        \"price\": 999.99,\n        \"description\": \"High-performance laptop\",\n        \"createdBy\": \"admin\",\n        \"createdOn\": \"2024-01-15T10:30:00\",\n        \"available\": true\n    }\n]"}]}, {"name": "Get Product by ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has product data', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.expect(jsonData).to.have.property('code');", "    pm.expect(jsonData).to.have.property('product');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/products/{{product_id}}", "host": ["{{base_url}}"], "path": ["api", "products", "{{product_id}}"]}, "description": "Get a specific product by its ID. Requires USER, MODERATOR, or ADMIN role."}, "response": [{"name": "Product Found", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/products/1", "host": ["{{base_url}}"], "path": ["api", "products", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"id\": 1,\n    \"code\": \"PROD001\",\n    \"product\": \"Laptop\",\n    \"brand\": \"Dell\",\n    \"category\": \"Electronics\",\n    \"color\": \"Black\",\n    \"price\": 999.99,\n    \"description\": \"High-performance laptop\",\n    \"createdBy\": \"admin\",\n    \"createdOn\": \"2024-01-15T10:30:00\",\n    \"available\": true\n}"}, {"name": "Product Not Found", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/products/999", "host": ["{{base_url}}"], "path": ["api", "products", "999"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": ""}]}, {"name": "Create Product", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Product created successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "    pm.collectionVariables.set('product_id', jsonData.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"code\": \"PROD001\",\n    \"name\": \"Gaming Laptop\",\n    \"brandName\": \"ASUS\",\n    \"category\": \"Electronics\",\n    \"color\": \"Black\",\n    \"price\": 1299.99,\n    \"description\": \"High-performance gaming laptop with RTX graphics\",\n    \"thumbnailImage\": \"laptop_main.jpg\",\n    \"subImages\": \"laptop_side.jpg,laptop_back.jpg\",\n    \"availability\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/products", "host": ["{{base_url}}"], "path": ["api", "products"]}, "description": "Create a new product. Requires MODERATOR or ADMIN role."}, "response": [{"name": "Product Created Successfully", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"code\": \"PROD001\",\n    \"name\": \"Gaming Laptop\",\n    \"brandName\": \"ASUS\",\n    \"category\": \"Electronics\",\n    \"color\": \"Black\",\n    \"price\": 1299.99,\n    \"description\": \"High-performance gaming laptop\",\n    \"thumbnailImage\": \"laptop_main.jpg\",\n    \"subImages\": \"laptop_side.jpg,laptop_back.jpg\",\n    \"availability\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/products", "host": ["{{base_url}}"], "path": ["api", "products"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"id\": 1,\n    \"code\": \"PROD001\",\n    \"product\": \"Gaming Laptop\",\n    \"brand\": \"ASUS\",\n    \"category\": \"Electronics\",\n    \"color\": \"Black\",\n    \"price\": 1299.99,\n    \"description\": \"High-performance gaming laptop\",\n    \"createdBy\": \"admin\",\n    \"createdOn\": \"2024-01-15T10:30:00\",\n    \"available\": true\n}"}, {"name": "Product Code Already Exists", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"code\": \"PROD001\",\n    \"name\": \"Duplicate Product\",\n    \"brandName\": \"Test\",\n    \"category\": \"Test\",\n    \"price\": 100.00\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/products", "host": ["{{base_url}}"], "path": ["api", "products"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"message\": \"Error: Product code already exists!\"\n}"}]}, {"name": "Update Product", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Product updated successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('id');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"code\": \"PROD001\",\n    \"name\": \"Updated Gaming Laptop\",\n    \"brandName\": \"ASUS ROG\",\n    \"category\": \"Electronics\",\n    \"color\": \"Red\",\n    \"price\": 1499.99,\n    \"description\": \"Updated high-performance gaming laptop with RTX 4080\",\n    \"thumbnailImage\": \"laptop_updated.jpg\",\n    \"subImages\": \"laptop_updated_side.jpg\",\n    \"availability\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/products/{{product_id}}", "host": ["{{base_url}}"], "path": ["api", "products", "{{product_id}}"]}, "description": "Update an existing product. Requires MODERATOR or ADMIN role."}, "response": [{"name": "Product Updated Successfully", "originalRequest": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"code\": \"PROD001\",\n    \"name\": \"Updated Gaming Laptop\",\n    \"brandName\": \"ASUS ROG\",\n    \"category\": \"Electronics\",\n    \"color\": \"Red\",\n    \"price\": 1499.99,\n    \"description\": \"Updated gaming laptop\",\n    \"availability\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/products/1", "host": ["{{base_url}}"], "path": ["api", "products", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"id\": 1,\n    \"code\": \"PROD001\",\n    \"product\": \"Updated Gaming Laptop\",\n    \"brand\": \"ASUS ROG\",\n    \"category\": \"Electronics\",\n    \"color\": \"Red\",\n    \"price\": 1499.99,\n    \"description\": \"Updated gaming laptop\",\n    \"createdBy\": \"admin\",\n    \"createdOn\": \"2024-01-15T10:30:00\",\n    \"available\": true\n}"}]}, {"name": "Delete Product", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Product deleted successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.include('deleted successfully');", "});"], "type": "text/javascript"}}], "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/products/{{product_id}}", "host": ["{{base_url}}"], "path": ["api", "products", "{{product_id}}"]}, "description": "Delete a product. Requires ADMIN role only."}, "response": [{"name": "Product Deleted Successfully", "originalRequest": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/products/1", "host": ["{{base_url}}"], "path": ["api", "products", "1"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n    \"message\": \"Product deleted successfully\"\n}"}, {"name": "Product Not Found", "originalRequest": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/api/products/999", "host": ["{{base_url}}"], "path": ["api", "products", "999"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": ""}]}]}]}