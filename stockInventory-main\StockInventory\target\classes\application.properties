# Database Configuration
spring.datasource.url=***********************************************************************************************************************
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:6666}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Connection Pool Configuration
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=${SHOW_SQL:false}
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# JWT Configuration
app.jwtSecret=${JWT_SECRET:m7XJV0aSND0tFSBDwIxg2sjqOqWSRcnpvQ+DeusqXYG4v2PSIixOR2+Yg4u5vJO4EDrdr99wwU9tnrmNjARrzA==}
app.jwtExpirationMs=${JWT_EXPIRATION:86400000}

# Server Configuration
server.port=${SERVER_PORT:8080}
server.error.include-message=always
server.error.include-binding-errors=always

# File upload settings
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
spring.servlet.multipart.enabled=true

# Thymeleaf Configuration
spring.thymeleaf.check-template-location=false

# JPA Configuration - Disable open-in-view warning
spring.jpa.open-in-view=false

# Logging Configuration
logging.level.com.example.stockinventorysystem=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=WARN
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# Security Configuration
spring.security.require-ssl=false

# Validation Configuration
spring.jackson.deserialization.fail-on-unknown-properties=false